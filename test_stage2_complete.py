#!/usr/bin/env python3
"""
第二阶段完整测试
Stage 2 Complete Test
"""

import sys
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web.utils.monitor_integration import MonitorIntegration

def test_stage2_complete():
    """第二阶段完整测试"""
    print("🔍 第二阶段完整功能测试")
    print("=" * 60)
    
    # 创建监控集成实例
    monitor_integration = MonitorIntegration()
    
    # 测试计数器
    tests_passed = 0
    tests_failed = 0
    
    def test_result(test_name, success, details=""):
        nonlocal tests_passed, tests_failed
        if success:
            tests_passed += 1
            print(f"✅ {test_name}")
            if details:
                print(f"   {details}")
        else:
            tests_failed += 1
            print(f"❌ {test_name}")
            if details:
                print(f"   {details}")
    
    print("\n📋 第二阶段功能测试清单")
    print("-" * 60)
    
    # 任务2.1: 监控系统启停控制测试
    print("\n🎯 任务2.1: 监控系统启停控制")
    print("-" * 30)
    
    # 1. 获取初始状态
    try:
        initial_status = monitor_integration.get_monitor_status()
        test_result(
            "获取监控状态", 
            'error' not in initial_status,
            f"运行状态: {'运行中' if initial_status.get('is_running', False) else '未运行'}"
        )
    except Exception as e:
        test_result("获取监控状态", False, f"异常: {e}")
    
    # 2. 启动监控系统
    try:
        start_result = monitor_integration.start_monitor()
        test_result(
            "启动监控系统", 
            start_result.get('success', False),
            start_result.get('message', 'N/A')
        )
        
        if start_result.get('success', False):
            time.sleep(3)  # 等待启动完成
            
            # 3. 检查启动后状态
            running_status = monitor_integration.get_monitor_status()
            test_result(
                "启动后状态检查", 
                running_status.get('is_running', False),
                f"线程活跃: {running_status.get('thread_alive', False)}"
            )
            
            # 4. 测试重复启动保护
            duplicate_start = monitor_integration.start_monitor()
            test_result(
                "重复启动保护", 
                not duplicate_start.get('success', True),
                "正确拒绝重复启动"
            )
            
            # 5. 停止监控系统
            stop_result = monitor_integration.stop_monitor()
            test_result(
                "停止监控系统", 
                stop_result.get('success', False),
                stop_result.get('message', 'N/A')
            )
            
            time.sleep(1)  # 等待停止完成
            
            # 6. 检查停止后状态
            stopped_status = monitor_integration.get_monitor_status()
            test_result(
                "停止后状态检查", 
                not stopped_status.get('is_running', True),
                "监控系统已停止"
            )
    except Exception as e:
        test_result("监控启停控制", False, f"异常: {e}")
    
    # 任务2.2: 配置参数实时应用测试
    print("\n🎯 任务2.2: 配置参数实时应用")
    print("-" * 30)
    
    # 7. 获取当前配置
    try:
        current_config = monitor_integration.get_monitor_config()
        test_result(
            "获取监控配置", 
            'error' not in current_config,
            f"配置项数: {len(current_config)}"
        )
    except Exception as e:
        test_result("获取监控配置", False, f"异常: {e}")
    
    # 8. 配置验证测试
    try:
        # 测试有效配置
        valid_config = {
            'max_symbols': 75,
            'confidence_threshold': 70.0,
            'log_level': 'DEBUG'
        }
        
        valid_result = monitor_integration.update_monitor_config(valid_config)
        test_result(
            "有效配置更新", 
            valid_result.get('success', False),
            "配置验证通过"
        )
        
        # 测试无效配置
        invalid_config = {'max_symbols': -5}
        invalid_result = monitor_integration.update_monitor_config(invalid_config)
        test_result(
            "无效配置拒绝", 
            not invalid_result.get('success', True),
            "正确拒绝无效配置"
        )
    except Exception as e:
        test_result("配置验证", False, f"异常: {e}")
    
    # 9. 配置文件读写测试
    try:
        auto_config = monitor_integration._load_config_file('auto_monitor.json')
        monitoring_config = monitor_integration._load_config_file('monitoring.json')
        
        test_result(
            "配置文件读取", 
            len(auto_config) > 0 and len(monitoring_config) > 0,
            f"auto_monitor: {len(auto_config)}项, monitoring: {len(monitoring_config)}项"
        )
    except Exception as e:
        test_result("配置文件读写", False, f"异常: {e}")
    
    # 10. 热更新测试
    try:
        # 启动监控系统进行热更新测试
        start_result = monitor_integration.start_monitor()
        if start_result.get('success', False):
            time.sleep(2)
            
            hot_config = {
                'log_level': 'WARNING',
                'enable_signal_notification': False
            }
            
            hot_result = monitor_integration.apply_config_hot_update(hot_config)
            test_result(
                "配置热更新", 
                hot_result.get('success', False),
                f"应用: {hot_result.get('applied_count', 0)}项"
            )
            
            # 停止监控系统
            monitor_integration.stop_monitor()
            time.sleep(1)
        else:
            test_result("配置热更新", False, "无法启动监控系统进行热更新测试")
    except Exception as e:
        test_result("配置热更新", False, f"异常: {e}")
    
    # 任务2.3: 系统健康监控测试
    print("\n🎯 任务2.3: 系统健康监控")
    print("-" * 30)
    
    # 11. 监控系统健康检查
    try:
        monitor_health = monitor_integration._check_monitor_health()
        test_result(
            "监控系统健康检查", 
            'error' not in monitor_health,
            f"问题: {len(monitor_health.get('issues', []))}个, 警告: {len(monitor_health.get('warnings', []))}个"
        )
    except Exception as e:
        test_result("监控系统健康检查", False, f"异常: {e}")
    
    # 12. 数据库健康检查
    try:
        database_health = monitor_integration._check_database_health()
        test_result(
            "数据库健康检查", 
            'error' not in database_health,
            f"数据库: {len(database_health.get('databases', {}))}个"
        )
    except Exception as e:
        test_result("数据库健康检查", False, f"异常: {e}")
    
    # 13. 日志健康检查
    try:
        log_health = monitor_integration._check_log_health()
        test_result(
            "日志健康检查", 
            'error' not in log_health,
            f"日志文件: {len(log_health.get('log_files', {}))}个"
        )
    except Exception as e:
        test_result("日志健康检查", False, f"异常: {e}")
    
    # 14. 网络健康检查
    try:
        network_health = monitor_integration._check_network_health()
        test_result(
            "网络健康检查", 
            'error' not in network_health,
            f"网络连接: {'正常' if network_health.get('status', {}).get('internet_accessible', False) else '异常'}"
        )
    except Exception as e:
        test_result("网络健康检查", False, f"异常: {e}")
    
    # 15. 性能指标获取
    try:
        performance_metrics = monitor_integration._get_performance_metrics()
        test_result(
            "性能指标获取", 
            'error' not in performance_metrics,
            f"指标项: {len([k for k in performance_metrics.keys() if not k.endswith('_error')])}个"
        )
    except Exception as e:
        test_result("性能指标获取", False, f"异常: {e}")
    
    # 16. 完整系统健康检查
    try:
        system_health = monitor_integration.get_system_health()
        test_result(
            "完整系统健康检查", 
            'error' not in system_health,
            f"整体状态: {system_health.get('overall_status', 'unknown').upper()}"
        )
    except Exception as e:
        test_result("完整系统健康检查", False, f"异常: {e}")
    
    # 17. 状态文件管理
    try:
        status_file = monitor_integration.status_file
        test_result(
            "状态文件管理", 
            status_file.exists(),
            f"状态文件: {status_file.name}"
        )
    except Exception as e:
        test_result("状态文件管理", False, f"异常: {e}")
    
    # 18. 配置备份机制
    try:
        # 先触发一次配置更新以创建备份文件
        backup_test_config = {
            'max_symbols': 60,
            'confidence_threshold': 65.0
        }
        backup_update_result = monitor_integration.update_monitor_config(backup_test_config)

        # 检查备份文件
        auto_backup = Path("config/auto_monitor.bak")
        monitoring_backup = Path("config/monitoring.bak")

        backup_exists = auto_backup.exists() or monitoring_backup.exists()

        test_result(
            "配置备份机制",
            backup_exists,
            f"auto备份: {'✅' if auto_backup.exists() else '❌'}, monitoring备份: {'✅' if monitoring_backup.exists() else '❌'}"
        )

        # 恢复原始配置
        restore_config = {
            'max_symbols': 50,
            'confidence_threshold': 60.0
        }
        monitor_integration.update_monitor_config(restore_config)

    except Exception as e:
        test_result("配置备份机制", False, f"异常: {e}")
    
    # 恢复原始配置
    try:
        restore_config = {
            'max_symbols': 50,
            'confidence_threshold': 60.0,
            'log_level': 'INFO',
            'enable_signal_notification': True
        }
        monitor_integration.update_monitor_config(restore_config)
    except:
        pass
    
    # 测试总结
    print("\n" + "=" * 60)
    print("🎯 第二阶段测试总结")
    print("=" * 60)
    
    total_tests = tests_passed + tests_failed
    success_rate = (tests_passed / total_tests * 100) if total_tests > 0 else 0
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {tests_passed} ✅")
    print(f"失败测试: {tests_failed} ❌")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("\n🎉 测试结果: 优秀！第二阶段功能完全正常")
    elif success_rate >= 80:
        print("\n👍 测试结果: 良好！大部分功能正常工作")
    elif success_rate >= 70:
        print("\n⚠️ 测试结果: 一般，需要修复部分问题")
    else:
        print("\n❌ 测试结果: 需要重大修复")
    
    print("\n📋 第二阶段功能清单:")
    print("   ✅ 监控系统启停控制")
    print("   ✅ 配置参数实时应用")
    print("   ✅ 系统健康监控")
    print("   ✅ 状态持久化管理")
    print("   ✅ 错误处理和异常恢复")
    
    return success_rate >= 80

if __name__ == "__main__":
    test_stage2_complete()
