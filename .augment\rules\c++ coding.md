---
### **一、 文件与代码布局 (File & Code Layout)**

代码的物理组织是协作的基础。

#### **1.1 文件组织**

*   **原则**：清晰、一致、易于定位。
*   **建议**：
    *   **文件命名**：推荐使用 `snake_case`（下划线命名法），如 `http_client.h`。这与C++标准库和诸多流行库（如Boost）风格一致。
    *   **头文件保护**：使用 `#pragma once` 或传统的 `#ifndef/#define/#endif` 宏保护来防止头文件重复包含。`#pragma once` 更简洁且被主流编译器广泛支持。
*   **理由**：统一的命名风格便于脚本处理和快速识别。头文件保护是防止编译错误的必要措施。
*   **示例**：
    ```cpp
    // http_client.h
    #pragma once // 推荐

    // 或传统方式
    // #ifndef PROJECT_HTTP_CLIENT_H
    // #define PROJECT_HTTP_CLIENT_H
    
    // ... 头文件内容 ...

    // #endif // PROJECT_HTTP_CLIENT_H
    ```

#### **1.2 目录结构**

*   **原则**：按功能模块划分，实现高内聚、低耦合。
*   **建议**：将项目源码、头文件、第三方库、构建产物等分置于不同目录。
*   **理由**：结构化的目录使项目脉络清晰，便于新成员理解项目和自动化构建。
*   **示例**：
    ```
    project/
    ├── include/          // 对外暴露的头文件
    │   └── my_lib/
    │       └── http_client.h
    ├── src/              // 源文件
    │   ├── http/
    │   │   └── http_client.cpp
    │   └── utils/
    │       ├── string_utils.h
    │       └── string_utils.cpp
    ├── third_party/      // 第三方库
    ├── build/            // 构建输出目录
    └── main.cpp          // 主程序入口
    ```

#### **1.3 代码格式**

*   **原则**：保持视觉一致性，降低阅读认知负荷。
*   **建议**：
    *   **缩进**：使用 **4个空格** 进行缩进，不要使用Tab。
    *   **大括号**：推荐将左大括号`{`置于控制语句（`if`, `for`, `class`等）的 **同一行末尾** (K&R 风格)。
    *   **行长度**：建议每行不超过 **100或120个字符**。超长行应在逗号、操作符后等合适位置换行。
    *   **空格**：在二元操作符（`+`, `=`, `<`等）前后、逗号后、分号后（`for`循环中）添加空格。
*   **理由**：统一的格式是代码可读性的基石。使用空格缩进可避免不同编辑器对Tab宽度解析不一的问题。
*   **示例**：
    ```cpp
    // 正确格式
    void process_data(int count) {
        if (is_valid(count)) {
            auto result = complex_function(
                arg1, arg2, long_argument_name
            );
            // ...
        }
    }
    ```
type: "agent_requested"
description: "当进行c++代码编写修改审查时必须遵守此规范"
---
### **二、 命名规范 (Naming Conventions)**

见名知意是优秀代码的特征。

| 元素 | 命名法 | 示例 |
| :--- | :--- | :--- |
| **类 / 结构体 / 枚举类** | `UpperCamelCase` (大驼峰) | `class HttpConnection;` `struct Point;` |
| **函数 / 方法** | `snake_case` 或 `lowerCamelCase` | `get_user_info()` 或 `getUserInfo()` |
| **变量 (包括成员变量)** | `snake_case` | `int user_count;` `std::string file_path;` |
| **私有/保护成员变量** | `snake_case` + 后缀 `_` | `std::string name_;` `int port_;` |
| **常量 / 枚举值** | `k` + `UpperCamelCase` 或 `ALL_CAPS` | `const int kMaxConnections = 100;` `enum Color { RED, BLUE };` |
| **宏** | `ALL_CAPS` (全大写) | `#define MAX_BUFFER_SIZE 1024` |
| **命名空间** | `snake_case` (小写) | `namespace my_project { ... }` |

*   **函数命名**：推荐采用 **动词或动词+名词** 结构，清晰表达其行为，如 `calculate_sum()`、`is_empty()`。

---

### **三、 编程实践 (Programming Practices)**

编写健壮、高效且易于维护的代码。

#### **3.1 类型与变量**

*   **原则**：类型安全，意图明确。
*   **建议**：
    *   **固定宽度整数**：涉及序列化、跨平台或精确位宽要求的场景，使用 `<cstdint>` 中的类型，如 `int32_t`, `uint64_t`。
    *   **初始化**：所有变量在声明时必须初始化。
    *   **`auto` 的使用**：在类型名冗长（尤其是迭代器和模板）时，使用 `auto` 提高简洁性。但当类型不明显时，显式声明类型以增强可读性。
    *   **空指针**：始终使用 `nullptr`，而非 `NULL` 或 `0`。

#### **3.2 资源管理 (RAII)**

*   **原则**：**资源获取即初始化 (RAII)**，利用对象生命周期自动管理资源。
*   **建议**：
    *   **智能指针**：使用 `std::unique_ptr` 表示资源的独占所有权，使用 `std::shared_ptr` 表示共享所有权。优先使用 `std::make_unique` 和 `std::make_shared`。
    *   **禁止裸指针**：避免手动 `new` 和 `delete`，除非在实现底层数据结构或与C库交互时。
    *   **锁管理**：使用 `std::lock_guard` 或 `std::unique_lock` 自动管理互斥锁。
*   **理由**：RAII是C++异常安全和防止资源泄漏的核心机制。
*   **示例**：
    ```cpp
    // 正确：RAII
    void process_resource() {
        auto resource = std::make_unique<Resource>(); // 自动管理
        // ... 使用 resource，发生异常时也会自动释放
    }

    // 错误：手动管理，易泄漏
    void process_resource_bad() {
        Resource* resource = new Resource();
        if (some_condition) {
            delete resource; // 提前返回，可能忘记delete
            return;
        }
        // ...
        delete resource; // 如果这里发生异常，delete不会被执行
    }
    ```

#### **3.3 函数设计**

*   **原则**：单一职责，接口清晰。
*   **建议**：
    *   **函数长度**：函数应简短，专注于做好一件事。过长的函数（如超过50行）是重构的信号。
    *   **参数传递**：
        *   对**只读**的大对象（如 `std::string`, `std::vector`）使用 `const&` 传递。
        *   对需要**修改**的对象使用 `&` 传递。
        *   对需要**拷贝或移动**所有权的对象，考虑**传值**并 `std::move`。
        *   对POD（Plain Old Data）类型或小型对象（如 `int`, `double`, 指针），直接**传值**。
    *   **返回值**：优先返回值而非输出参数，这更符合函数式编程思想，也便于链式调用。RVO/NRVO会处理大部分性能问题。
    *   **异常安全**：
        *   使用 `noexcept` 关键字明确标示不会抛出异常的函数。这有助于编译器优化。
        *   **不要使用 `throw(...)` 异常规范**，它已在C++17中被移除。
        *   在析构函数、移动操作和`swap`函数中应避免抛出异常。

#### **3.4 类设计**

*   **原则**：封装、继承和多态的正确运用。
*   **建议**：
    *   **访问控制**：默认将成员变量设为 `private`，通过公有接口 (`public`) 访问。
    *   **The Rule of Zero/Five**：如果类需要自定义析构函数、拷贝/移动构造函数或拷贝/移动赋值运算符中的任何一个，通常意味着需要全部定义或 `delete` 它们（Rule of Five）。如果类不直接管理资源，则不需要定义任何一个（Rule of Zero）。
    *   **显式禁用**：对于不可拷贝的类（如文件句柄、锁），使用 `= delete` 显式禁用拷贝构造和拷贝赋值。
    *   **`const` 正确性**：不修改对象状态的成员函数应声明为 `const`。
    *   **继承**：
        *   基类的析构函数必须是 `public virtual` 或 `protected non-virtual`。若希望通过基类指针删除派生类对象，则必须是 `virtual`。
        *   使用 `override` 关键字显式地重写虚函数，让编译器检查错误。
        *   使用 `final` 关键字防止类被继承或虚函数被进一步重写。
*   **示例**：
    ```cpp
    class Shape {
    public:
        virtual ~Shape() = default; // 虚析构函数，保证派生类正确析构
        virtual double area() const = 0; // 纯虚函数
    };

    class Circle final : public Shape { // final: Circle不能再被继承
    public:
        // ...
        double area() const override; // override: 明确重写基类虚函数
    private:
        double radius_;
    };
    ```

---

### **四、 现代C++特性运用**

拥抱现代C++，编写更简洁、安全、高效的代码。

*   **Lambda 表达式**：优先使用 Lambda 表达式替代简单的函数对象，使代码更紧凑。
*   **范围 `for` 循环**：优先使用范围 `for` 循环遍历容器，代码更简洁且不易出错。
*   **标准库**：充分利用标准库提供的容器（`vector`, `string`, `unordered_map`）、算法（`sort`, `find_if`）和并发工具（`thread`, `mutex`），避免重复造轮子。

---

### **五、 注释与文档**

注释是写给未来的自己和同事看的。

*   **文件头注释**：在每个文件（`.h` 和 `.cpp`）的开头，说明文件的主要功能、作者和创建日期。
*   **接口注释**：为 `public` 的类、函数和方法编写文档注释（如 Doxygen 风格），解释其功能、参数、返回值和可能抛出的异常。
*   **实现注释**：只在必要时为复杂的算法、代码技巧或“为什么这么做”提供注释。好的代码本身应具有自明性。

---

### **六、 代码审查清单 (Code Review Checklist)**

1.  **可读性**：命名是否清晰？格式是否一致？函数是否过长？
2.  **正确性**：逻辑是否正确？是否处理了所有边界情况？
3.  **资源管理**：是否存在内存/文件/锁泄漏？是否遵循RAII？`new`/`delete`是否成对出现？
4.  **异常安全**：关键代码是否具备基本的异常安全保证？析构函数是否会抛出异常？
5.  **性能**：是否存在不必要的拷贝？循环中是否有昂贵操作？算法复杂度是否合理？
6.  **现代C++**：是否使用了更现代、更安全的语言特性（如智能指针、`override`、`noexcept`）？
7.  **注释与文档**：公开接口是否有文档？复杂逻辑是否有解释？

---