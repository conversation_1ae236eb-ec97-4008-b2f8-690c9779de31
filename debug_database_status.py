#!/usr/bin/env python3
"""
调试数据库状态检查
Debug Database Status Check
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web.utils.tbtrade_integration import TBTradeIntegration

def debug_database_status():
    """调试数据库状态检查"""
    print("🔍 调试数据库状态检查...")
    
    integration = TBTradeIntegration()
    
    # 单独测试check_database_connection
    print("\n1. 单独测试check_database_connection:")
    try:
        db_status = integration.check_database_connection()
        print(f"   返回结果: {db_status}")
    except Exception as e:
        print(f"   异常: {e}")
        import traceback
        traceback.print_exc()
    
    # 单独测试get_system_status
    print("\n2. 单独测试get_system_status:")
    try:
        system_status = integration.get_system_status()
        print(f"   返回结果: {system_status}")
    except Exception as e:
        print(f"   异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_database_status()
