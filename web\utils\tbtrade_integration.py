"""
TBTrade系统集成接口
提供Web界面与现有TBTrade系统的集成功能
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import sqlite3
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 导入现有TBTrade模块
try:
    from src.strategies.backtest_engine import BacktestEngine
    from src.strategies.ema_dynamic_strategy import EMADynamicStrategy
    from src.data_layer.historical_data_fetcher import scan_local_databases
    from run_dynamic_backtest import DynamicBacktestRunner
    from backtest_visualization import BacktestVisualizer
except ImportError as e:
    print(f"警告: 无法导入TBTrade模块: {e}")
    # 在开发阶段可以继续运行，但功能会受限
    BacktestEngine = None
    EMADynamicStrategy = None

class TBTradeIntegration:
    """TBTrade系统集成类"""
    
    def __init__(self):
        """初始化集成接口"""
        self.project_root = project_root
        self.data_dir = project_root / "data"
        self.config_dir = project_root / "config"
        self.logs_dir = project_root / "logs"
        self.backtest_results_dir = project_root / "backtest_results"
        
        # 确保目录存在
        self._ensure_directories()
        
        # 初始化组件
        self.backtest_engine = None
        self.dynamic_runner = None
        self.visualizer = None
        
    def _ensure_directories(self):
        """确保必要的目录存在"""
        for directory in [self.data_dir, self.logs_dir, self.backtest_results_dir]:
            directory.mkdir(exist_ok=True)
    
    def get_available_symbols(self) -> List[str]:
        """获取可用的交易币种列表"""
        try:
            # 检查数据目录是否存在
            if not self.data_dir.exists():
                print(f"警告: 数据目录不存在: {self.data_dir}")
                return self._get_default_symbols()

            databases = scan_local_databases(str(self.data_dir))
            if not databases:
                print("警告: 未发现本地数据库")
                return self._get_default_symbols()

            symbols = []
            for db_info in databases:
                if 'symbols' in db_info and db_info['symbols']:
                    symbols.extend(db_info['symbols'])

            if not symbols:
                print("警告: 数据库中未找到币种数据")
                return self._get_default_symbols()

            return sorted(list(set(symbols)))
        except Exception as e:
            print(f"获取币种列表失败: {e}")
            return self._get_default_symbols()

    def _get_default_symbols(self) -> List[str]:
        """获取默认币种列表"""
        return ["BTCUSDT", "ETHUSDT", "ADAUSDT", "BNBUSDT", "SOLUSDT",
                "DOTUSDT", "LINKUSDT", "UNIUSDT", "LTCUSDT", "BCHUSDT"]

    def check_database_connection(self) -> Dict[str, Any]:
        """检查数据库连接状态"""
        status = {
            'connected': False,
            'data_dir_exists': False,
            'databases_found': 0,
            'total_symbols': 0,
            'total_klines': 0,
            'latest_update': None,
            'errors': []
        }

        try:
            # 检查数据目录
            status['data_dir_exists'] = self.data_dir.exists()
            if not status['data_dir_exists']:
                status['errors'].append(f"数据目录不存在: {self.data_dir}")
                return status

            # 扫描数据库
            databases = scan_local_databases(str(self.data_dir))
            status['databases_found'] = len(databases)

            if not databases:
                status['errors'].append("未发现本地数据库文件")
                return status

            # 统计数据
            for db_info in databases:
                status['total_symbols'] += db_info.get('unique_symbols', 0)
                status['total_klines'] += db_info.get('total_klines', 0)

                # 获取最新更新时间
                latest_date = db_info.get('latest_date')
                if latest_date:
                    if not status['latest_update'] or latest_date > status['latest_update']:
                        status['latest_update'] = latest_date

            # 测试数据库连接
            for db_info in databases:
                try:
                    conn = sqlite3.connect(str(db_info['db_file']))
                    cursor = conn.cursor()
                    cursor.execute(f"SELECT COUNT(*) FROM {db_info['table_name']} LIMIT 1")
                    conn.close()
                except Exception as e:
                    status['errors'].append(f"数据库连接测试失败 {db_info['db_file'].name}: {e}")
                    return status

            status['connected'] = True
            return status

        except Exception as e:
            status['errors'].append(f"数据库状态检查失败: {e}")
            return status

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态信息"""
        try:
            # 获取数据库连接状态
            db_status = self.check_database_connection()

            # 获取数据概览
            data_info = self.get_data_info()

            # 构建系统状态
            status = {
                'timestamp': datetime.now().isoformat(),
                'database': {
                    'connected': db_status['connected'],
                    'databases_count': db_status['databases_found'],
                    'total_symbols': db_status['total_symbols'],
                    'total_klines': db_status['total_klines'],
                    'latest_update': db_status['latest_update'],
                    'errors': db_status['errors']
                },
                'data': {
                    'available_symbols': len(self.get_available_symbols()),
                    'data_quality': 'good' if db_status['connected'] and db_status['total_klines'] > 0 else 'poor'
                },
                'system': {
                    'data_dir': str(self.data_dir),
                    'config_dir': str(self.config_dir),
                    'logs_dir': str(self.logs_dir)
                }
            }

            # 添加数据库详细信息
            if not isinstance(data_info, dict) or 'error' not in data_info:
                status['database']['details'] = data_info

            return status

        except Exception as e:
            return {
                'timestamp': datetime.now().isoformat(),
                'error': f'获取系统状态失败: {e}',
                'database': {'connected': False},
                'data': {'available_symbols': 0, 'data_quality': 'error'}
            }

    def trigger_data_update(self, symbols: List[str] = None, interval: str = '4h') -> Dict[str, Any]:
        """触发数据更新"""
        try:
            from src.data_layer.historical_data_fetcher import USDTHistoricalFetcher

            # 创建数据获取器
            fetcher = USDTHistoricalFetcher(use_proxy=False)  # 根据需要调整代理设置

            # 如果没有指定币种，获取当前可用的币种
            if not symbols:
                symbols = self.get_available_symbols()[:10]  # 限制为前10个币种避免过长时间

            # 获取最新的数据时间
            db_status = self.check_database_connection()
            latest_update = db_status.get('latest_update')

            if latest_update:
                # 从最后更新时间开始获取新数据
                start_time = datetime.strptime(latest_update, '%Y-%m-%d %H:%M:%S')
                end_time = datetime.now()
            else:
                # 如果没有历史数据，获取最近7天的数据
                end_time = datetime.now()
                start_time = end_time - timedelta(days=7)

            # 检查是否需要更新
            time_diff = (end_time - start_time).total_seconds() / 3600  # 小时差
            if time_diff < 4:  # 少于4小时不需要更新
                return {
                    'status': 'no_update_needed',
                    'message': f'数据已是最新，最后更新: {latest_update}',
                    'time_diff_hours': time_diff
                }

            # 执行数据更新
            result = {
                'status': 'updating',
                'symbols': symbols,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'interval': interval,
                'message': '数据更新已启动'
            }

            return result

        except Exception as e:
            return {
                'status': 'error',
                'error': f'触发数据更新失败: {e}',
                'timestamp': datetime.now().isoformat()
            }

    def get_data_update_status(self) -> Dict[str, Any]:
        """获取数据更新状态"""
        try:
            db_status = self.check_database_connection()

            if not db_status['connected']:
                return {
                    'status': 'database_error',
                    'message': '数据库连接失败',
                    'errors': db_status['errors']
                }

            latest_update = db_status.get('latest_update')
            if not latest_update:
                return {
                    'status': 'no_data',
                    'message': '未找到历史数据'
                }

            # 计算数据新鲜度
            last_update_time = datetime.strptime(latest_update, '%Y-%m-%d %H:%M:%S')
            now = datetime.now()
            hours_since_update = (now - last_update_time).total_seconds() / 3600

            # 判断数据状态
            if hours_since_update <= 4:
                status = 'fresh'
                message = '数据是最新的'
            elif hours_since_update <= 24:
                status = 'recent'
                message = '数据较新'
            elif hours_since_update <= 168:  # 7天
                status = 'stale'
                message = '数据需要更新'
            else:
                status = 'very_stale'
                message = '数据严重过时'

            return {
                'status': status,
                'message': message,
                'latest_update': latest_update,
                'hours_since_update': round(hours_since_update, 1),
                'total_symbols': db_status['total_symbols'],
                'total_klines': db_status['total_klines'],
                'update_recommended': hours_since_update > 4
            }

        except Exception as e:
            return {
                'status': 'error',
                'error': f'获取数据更新状态失败: {e}',
                'timestamp': datetime.now().isoformat()
            }
    
    def get_data_info(self, symbol: str = None) -> Dict[str, Any]:
        """获取数据信息"""
        try:
            # 检查数据目录
            if not self.data_dir.exists():
                return {'error': f'数据目录不存在: {self.data_dir}'}

            databases = scan_local_databases(str(self.data_dir))
            if not databases:
                return {'error': '未发现本地数据库'}

            if symbol:
                # 获取特定币种的数据信息
                for db_info in databases:
                    if 'symbols' in db_info and symbol in db_info.get('symbols', []):
                        return {
                            'symbol': symbol,
                            'database': str(db_info['db_file']),
                            'table': db_info['table_name'],
                            'total_klines': db_info.get('total_klines', 0),
                            'unique_symbols': db_info.get('unique_symbols', 0),
                            'date_range': {
                                'start': db_info.get('earliest_date'),
                                'end': db_info.get('latest_date')
                            },
                            'interval': db_info.get('interval', '4h'),
                            'time_label': db_info.get('time_label', 'unknown')
                        }
                return {'error': f'未找到币种 {symbol} 的数据'}
            else:
                # 获取所有数据概览
                total_symbols = sum(db_info.get('unique_symbols', 0) for db_info in databases)
                total_klines = sum(db_info.get('total_klines', 0) for db_info in databases)

                summary = {
                    'total_databases': len(databases),
                    'total_symbols': total_symbols,
                    'total_klines': total_klines,
                    'databases': []
                }

                for db_info in databases:
                    summary['databases'].append({
                        'db_file': db_info['db_file'].name,
                        'table_name': db_info['table_name'],
                        'time_label': db_info.get('time_label', 'unknown'),
                        'interval': db_info.get('interval', '4h'),
                        'unique_symbols': db_info.get('unique_symbols', 0),
                        'total_klines': db_info.get('total_klines', 0),
                        'date_range': {
                            'start': db_info.get('earliest_date'),
                            'end': db_info.get('latest_date')
                        }
                    })

                return summary
        except Exception as e:
            return {'error': f'获取数据信息失败: {e}'}
    
    def load_symbol_data(self, symbol: str, start_date: str = None, 
                        end_date: str = None) -> pd.DataFrame:
        """加载币种数据"""
        try:
            databases = scan_local_databases(str(self.data_dir))
            
            # 找到对应的数据库信息
            db_info = None
            for db in databases:
                # 修复：检查symbols列表中是否包含目标symbol
                if 'symbols' in db and symbol in db.get('symbols', []):
                    db_info = db
                    break
            
            if not db_info:
                raise ValueError(f"未找到币种 {symbol} 的数据")
            
            # 连接数据库
            conn = sqlite3.connect(str(db_info['db_file']))
            
            # 构建查询
            query = f"""
            SELECT datetime_str as datetime, open_price as open, high_price as high,
                   low_price as low, close_price as close, volume
            FROM {db_info['table_name']}
            WHERE symbol = ?
            """
            params = [symbol]
            
            if start_date:
                query += " AND datetime_str >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND datetime_str <= ?"
                params.append(end_date)
            
            query += " ORDER BY datetime_str"
            
            # 执行查询
            df = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            if df.empty:
                raise ValueError(f"未找到符合条件的数据")
            
            # 数据预处理
            df['datetime'] = pd.to_datetime(df['datetime'])
            df.set_index('datetime', inplace=True)
            
            # 确保数据类型正确
            for col in ['open', 'high', 'low', 'close', 'volume']:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            return df
            
        except Exception as e:
            raise Exception(f"加载数据失败: {e}")

    def _validate_backtest_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证回测配置参数"""
        try:
            # 检查必需参数
            required_fields = ['symbols', 'initial_capital', 'start_date', 'end_date']
            for field in required_fields:
                if field not in config:
                    return {'valid': False, 'error': f"缺少必需参数: {field}"}

            # 验证币种列表
            symbols = config['symbols']
            if not symbols or not isinstance(symbols, list):
                return {'valid': False, 'error': "币种列表不能为空且必须是列表格式"}

            if len(symbols) > 50:
                return {'valid': False, 'error': "币种数量不能超过50个"}

            # 验证初始资金
            initial_capital = config['initial_capital']
            if not isinstance(initial_capital, (int, float)) or initial_capital <= 0:
                return {'valid': False, 'error': "初始资金必须是正数"}

            if initial_capital < 1000:
                return {'valid': False, 'error': "初始资金不能少于1000"}

            # 验证日期格式
            start_date = config['start_date']
            end_date = config['end_date']

            try:
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            except ValueError:
                return {'valid': False, 'error': "日期格式错误，应为YYYY-MM-DD"}

            # 验证日期逻辑
            if start_dt >= end_dt:
                return {'valid': False, 'error': "开始日期必须早于结束日期"}

            # 验证时间范围（不超过3年）
            if (end_dt - start_dt).days > 365 * 3:
                return {'valid': False, 'error': "回测时间范围不能超过3年"}

            # 验证策略参数
            strategy_params = config.get('strategy_params', {})
            if strategy_params:
                param_validation = self._validate_strategy_params(strategy_params)
                if not param_validation['valid']:
                    return param_validation

            return {'valid': True, 'error': None}

        except Exception as e:
            return {'valid': False, 'error': f"参数验证异常: {str(e)}"}

    def _validate_strategy_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """验证策略参数"""
        try:
            # EMA参数验证
            if 'ema_short' in params:
                if not isinstance(params['ema_short'], int) or params['ema_short'] < 5 or params['ema_short'] > 50:
                    return {'valid': False, 'error': "短期EMA周期必须在5-50之间"}

            if 'ema_medium' in params:
                if not isinstance(params['ema_medium'], int) or params['ema_medium'] < 20 or params['ema_medium'] > 100:
                    return {'valid': False, 'error': "中期EMA周期必须在20-100之间"}

            if 'ema_long' in params:
                if not isinstance(params['ema_long'], int) or params['ema_long'] < 100 or params['ema_long'] > 300:
                    return {'valid': False, 'error': "长期EMA周期必须在100-300之间"}

            # 验证EMA周期逻辑关系
            ema_short = params.get('ema_short', 21)
            ema_medium = params.get('ema_medium', 55)
            ema_long = params.get('ema_long', 200)

            if ema_short >= ema_medium:
                return {'valid': False, 'error': "短期EMA周期必须小于中期EMA周期"}

            if ema_medium >= ema_long:
                return {'valid': False, 'error': "中期EMA周期必须小于长期EMA周期"}

            # 风险管理参数验证
            if 'stop_loss_pct' in params:
                stop_loss = params['stop_loss_pct']
                if not isinstance(stop_loss, (int, float)) or stop_loss <= 0 or stop_loss > 0.5:
                    return {'valid': False, 'error': "止损比例必须在0-50%之间"}

            if 'take_profit_pct' in params:
                take_profit = params['take_profit_pct']
                if not isinstance(take_profit, (int, float)) or take_profit <= 0 or take_profit > 2.0:
                    return {'valid': False, 'error': "止盈比例必须在0-200%之间"}

            return {'valid': True, 'error': None}

        except Exception as e:
            return {'valid': False, 'error': f"策略参数验证异常: {str(e)}"}

    def run_backtest(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """运行回测"""
        # 参数验证
        validation_result = self._validate_backtest_config(config)
        if not validation_result['valid']:
            return {
                'success': False,
                'error': f"参数验证失败: {validation_result['error']}",
                'config': config,
                'timestamp': datetime.now().isoformat()
            }

        try:
            if not EMADynamicStrategy:
                raise Exception("TBTrade模块未正确加载，请检查系统环境")

            # 解析配置
            symbols = config.get('symbols', ['BTCUSDT'])
            initial_capital = config.get('initial_capital', 10000)
            start_date = config.get('start_date')
            end_date = config.get('end_date')
            strategy_params = config.get('strategy_params', {})

            # 验证币种是否可用
            available_symbols = self.get_available_symbols()
            invalid_symbols = [s for s in symbols if s not in available_symbols]
            if invalid_symbols:
                raise ValueError(f"以下币种不可用: {invalid_symbols}")

            # 验证数据是否存在
            for symbol in symbols:
                try:
                    data = self.load_symbol_data(symbol, start_date, end_date)
                    if data.empty:
                        raise ValueError(f"币种 {symbol} 在指定时间范围内无数据")
                except Exception as e:
                    raise ValueError(f"币种 {symbol} 数据加载失败: {e}")

            # 创建动态回测运行器
            runner = DynamicBacktestRunner(initial_capital=initial_capital)

            # 设置策略参数到runner（runner会在内部创建策略）
            runner.enable_partial_profit = config.get('enable_partial_profit', True)
            runner.verbose_level = config.get('verbose_level', 1)

            # 如果有策略参数，需要通过其他方式传递
            # 由于DynamicBacktestRunner内部创建策略时没有传递strategy_params
            # 我们需要修改这个逻辑或者直接使用策略

            # 直接使用策略进行回测
            strategy = EMADynamicStrategy(
                strategy_params=strategy_params,
                initial_capital=initial_capital,
                enable_partial_profit=config.get('enable_partial_profit', True),
                verbose_level=config.get('verbose_level', 1)
            )

            # 加载数据并运行策略
            symbol_data = {}
            for symbol in symbols:
                data = self.load_symbol_data(symbol, start_date, end_date)
                # 降低最小数据点要求，适应短期回测
                min_data_points = 50  # 至少需要50个数据点（约8天的4小时数据）
                if not data.empty and len(data) >= min_data_points:
                    symbol_data[symbol] = data

            if not symbol_data:
                raise ValueError("无有效数据进行回测")

            # 运行策略回测
            results = strategy.simulate_real_time_trading(symbol_data)

            # 验证回测结果
            if not results or not isinstance(results, dict):
                raise Exception("回测执行失败，未返回有效结果")

            return {
                'success': True,
                'results': results,
                'config': config,
                'timestamp': datetime.now().isoformat(),
                'symbols_count': len(symbols),
                'data_range': f"{start_date} 至 {end_date}"
            }

        except ValueError as e:
            # 参数或数据相关错误
            return {
                'success': False,
                'error': str(e),
                'error_type': 'validation_error',
                'config': config,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            # 其他系统错误
            return {
                'success': False,
                'error': f"回测执行失败: {str(e)}",
                'error_type': 'execution_error',
                'config': config,
                'timestamp': datetime.now().isoformat()
            }

    
    def get_recent_logs(self, log_type: str = 'trading', lines: int = 100) -> List[str]:
        """获取最近的日志"""
        try:
            log_file = self.logs_dir / f"{log_type}.log"
            
            if not log_file.exists():
                return []
            
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                return all_lines[-lines:] if len(all_lines) > lines else all_lines
                
        except Exception as e:
            return [f"读取日志失败: {e}"]
    
    def update_data(self, symbols: List[str] = None) -> Dict[str, Any]:
        """更新数据"""
        try:
            # 这里应该调用数据更新功能
            # 由于现有系统的数据更新逻辑比较复杂，这里先返回模拟结果
            
            if not symbols:
                symbols = self.get_available_symbols()[:5]  # 默认更新前5个币种
            
            result = {
                'success': True,
                'updated_symbols': symbols,
                'timestamp': datetime.now().isoformat(),
                'message': f'已触发 {len(symbols)} 个币种的数据更新'
            }
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

# 创建全局集成实例
tbtrade_integration = TBTradeIntegration()
