#!/usr/bin/env python3
"""
测试数据库连接功能
Test Database Connection Functionality
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web.utils.tbtrade_integration import TBTradeIntegration

def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试TBTrade数据库连接...")
    
    # 创建集成实例
    integration = TBTradeIntegration()
    
    # 1. 测试数据库连接状态
    print("\n1. 检查数据库连接状态:")
    db_status = integration.check_database_connection()
    print(f"   连接状态: {'✅ 正常' if db_status['connected'] else '❌ 异常'}")
    print(f"   数据目录存在: {'✅' if db_status['data_dir_exists'] else '❌'}")
    print(f"   发现数据库: {db_status['databases_found']} 个")
    print(f"   总币种数: {db_status['total_symbols']}")
    print(f"   总K线数: {db_status['total_klines']:,}")
    print(f"   最新更新: {db_status['latest_update']}")
    
    if db_status['errors']:
        print("   错误信息:")
        for error in db_status['errors']:
            print(f"     - {error}")
    
    # 2. 测试获取可用币种
    print("\n2. 获取可用币种:")
    symbols = integration.get_available_symbols()
    print(f"   可用币种数量: {len(symbols)}")
    print(f"   前10个币种: {symbols[:10]}")
    
    # 3. 测试获取数据信息
    print("\n3. 获取数据概览:")
    data_info = integration.get_data_info()
    if 'error' in data_info:
        print(f"   ❌ 错误: {data_info['error']}")
    else:
        print(f"   数据库数量: {data_info.get('total_databases', 0)}")
        print(f"   总币种数: {data_info.get('total_symbols', 0)}")
        print(f"   总K线数: {data_info.get('total_klines', 0):,}")
        
        if 'databases' in data_info:
            print("   数据库详情:")
            for db in data_info['databases'][:3]:  # 只显示前3个
                print(f"     - {db['db_file']}: {db['unique_symbols']}币种, {db['total_klines']:,}K线")
    
    # 4. 测试系统状态
    print("\n4. 获取系统状态:")
    try:
        system_status = integration.get_system_status()
        print(f"   时间戳: {system_status['timestamp']}")

        if 'error' in system_status:
            print(f"   ❌ 错误: {system_status['error']}")
        else:
            print(f"   数据库连接: {'✅' if system_status.get('database', {}).get('connected', False) else '❌'}")
            print(f"   数据质量: {system_status.get('data', {}).get('data_quality', 'unknown')}")
            print(f"   可用币种: {system_status.get('data', {}).get('available_symbols', 0)}")
    except Exception as e:
        print(f"   ❌ 获取系统状态失败: {e}")
    
    # 5. 测试加载具体币种数据
    if symbols:
        test_symbol = symbols[0]
        print(f"\n5. 测试加载币种数据 ({test_symbol}):")
        try:
            df = integration.load_symbol_data(test_symbol)
            print(f"   ✅ 成功加载 {test_symbol} 数据")
            print(f"   数据行数: {len(df)}")
            print(f"   时间范围: {df.index.min()} ~ {df.index.max()}")
            print(f"   数据列: {list(df.columns)}")
            print(f"   最新价格: {df['close'].iloc[-1]:.4f}")
        except Exception as e:
            print(f"   ❌ 加载失败: {e}")

if __name__ == "__main__":
    test_database_connection()
