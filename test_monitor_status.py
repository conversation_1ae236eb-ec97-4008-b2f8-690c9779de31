#!/usr/bin/env python3
"""
测试监控状态获取功能
Test Monitor Status Functionality
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web.utils.tbtrade_integration import TBTradeIntegration

def test_monitor_status():
    """测试监控状态获取"""
    print("🔍 测试监控状态获取功能...")
    
    # 创建集成实例
    integration = TBTradeIntegration()
    
    # 1. 测试监控状态获取
    print("\n1. 获取监控系统状态:")
    monitor_status = integration.get_monitor_status()
    
    print(f"   运行状态: {'🟢 运行中' if monitor_status['running'] else '🔴 未运行'}")
    print(f"   状态消息: {monitor_status['status_message']}")
    print(f"   监控币种数: {monitor_status['monitored_symbols']}")
    print(f"   运行时间: {monitor_status['uptime_seconds']} 秒")
    print(f"   最后更新: {monitor_status['last_update'] or 'N/A'}")
    print(f"   当前K线时间: {monitor_status['current_kline_time'] or 'N/A'}")
    print(f"   下个K线时间: {monitor_status['next_kline_time'] or 'N/A'}")
    
    if 'error' in monitor_status:
        print(f"   错误: {monitor_status['error']}")
    
    # 2. 测试完整系统状态（包含监控状态）
    print("\n2. 获取完整系统状态:")
    system_status = integration.get_system_status()
    
    if 'error' not in system_status:
        print(f"   时间戳: {system_status['timestamp']}")
        
        # 数据库状态
        db_info = system_status.get('database', {})
        print(f"   数据库连接: {'✅' if db_info.get('connected', False) else '❌'}")
        print(f"   总币种数: {db_info.get('total_symbols', 0)}")
        print(f"   总K线数: {db_info.get('total_klines', 0):,}")
        
        # 监控状态
        monitor_info = system_status.get('monitor', {})
        print(f"   监控运行: {'✅' if monitor_info.get('running', False) else '❌'}")
        print(f"   监控状态: {monitor_info.get('status_message', 'unknown')}")
        print(f"   监控币种: {monitor_info.get('monitored_symbols', 0)}")
        
        # 数据质量
        data_info = system_status.get('data', {})
        print(f"   数据质量: {data_info.get('data_quality', 'unknown')}")
        print(f"   可用币种: {data_info.get('available_symbols', 0)}")
        
    else:
        print(f"   错误: {system_status['error']}")
    
    # 3. 测试K线时间计算
    print("\n3. K线时间计算验证:")
    from datetime import datetime
    
    now = datetime.now()
    print(f"   当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 验证4小时K线时间计算
    current_hour = now.hour
    current_4h_hour = (current_hour // 4) * 4
    next_4h_hour = ((current_hour // 4) + 1) * 4
    
    print(f"   当前小时: {current_hour}")
    print(f"   当前4H时间点: {current_4h_hour}:00")
    print(f"   下个4H时间点: {next_4h_hour if next_4h_hour < 24 else 0}:00")
    
    # 4. 测试日志文件检查
    print("\n4. 日志文件检查:")
    log_file = integration.logs_dir / "trading.log"
    status_file = integration.logs_dir / "monitor_status.json"
    
    print(f"   日志目录: {integration.logs_dir}")
    print(f"   交易日志存在: {'✅' if log_file.exists() else '❌'}")
    print(f"   状态文件存在: {'✅' if status_file.exists() else '❌'}")
    
    if log_file.exists():
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            print(f"   日志行数: {len(lines)}")
            
            # 显示最后几行日志
            if lines:
                print("   最后3行日志:")
                for line in lines[-3:]:
                    print(f"     {line.strip()}")
        except Exception as e:
            print(f"   读取日志失败: {e}")
    
    # 5. 测试缓存状态
    print("\n5. 缓存状态:")
    cache_status = integration.get_cache_status()
    print(f"   缓存项数: {cache_status['total_cached_items']}")
    
    for key, details in cache_status['cache_details'].items():
        print(f"   {key}: {'✅有效' if details['valid'] else '❌过期'} ({details['age_seconds']:.1f}s)")

if __name__ == "__main__":
    test_monitor_status()
