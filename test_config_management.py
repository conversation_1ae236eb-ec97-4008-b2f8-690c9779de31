#!/usr/bin/env python3
"""
测试配置管理功能
Test Configuration Management Functionality
"""

import sys
import json
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web.utils.monitor_integration import MonitorIntegration

def test_config_management():
    """测试配置管理功能"""
    print("🔍 测试配置管理功能...")
    
    # 创建监控集成实例
    monitor_integration = MonitorIntegration()
    
    # 1. 测试获取当前配置
    print("\n1. 获取当前配置:")
    current_config = monitor_integration.get_monitor_config()
    
    if 'error' not in current_config:
        print("   ✅ 配置获取成功")
        print(f"   监控间隔: {current_config.get('monitor_interval', 'N/A')}")
        print(f"   最大币种数: {current_config.get('max_symbols', 'N/A')}")
        print(f"   最小交易量: {current_config.get('min_volume_usdt', 'N/A'):,}")
        print(f"   置信度阈值: {current_config.get('confidence_threshold', 'N/A')}%")
        print(f"   优先币种数: {len(current_config.get('priority_symbols', []))}")
        print(f"   排除币种数: {len(current_config.get('exclude_symbols', []))}")
        print(f"   日志级别: {current_config.get('log_level', 'N/A')}")
    else:
        print(f"   ❌ 配置获取失败: {current_config['error']}")
        return
    
    # 2. 测试配置验证 - 有效配置
    print("\n2. 测试配置验证 - 有效配置:")
    valid_config = {
        'max_symbols': 75,
        'min_volume_usdt': 2000000,
        'confidence_threshold': 70.0,
        'monitor_interval': '2h',
        'log_level': 'DEBUG',
        'enable_signal_notification': False
    }
    
    update_result = monitor_integration.update_monitor_config(valid_config)
    print(f"   更新结果: {'✅ 成功' if update_result.get('success', False) else '❌ 失败'}")
    print(f"   消息: {update_result.get('message', 'N/A')}")
    if update_result.get('requires_restart'):
        print("   ⚠️ 需要重启监控系统")
    
    # 3. 测试配置验证 - 无效配置
    print("\n3. 测试配置验证 - 无效配置:")
    invalid_configs = [
        {
            'name': '无效的max_symbols',
            'config': {'max_symbols': -5}
        },
        {
            'name': '无效的confidence_threshold',
            'config': {'confidence_threshold': 150}
        },
        {
            'name': '无效的monitor_interval',
            'config': {'monitor_interval': '3h'}
        },
        {
            'name': '无效的log_level',
            'config': {'log_level': 'INVALID'}
        },
        {
            'name': '无效的priority_symbols',
            'config': {'priority_symbols': ['INVALID']}
        }
    ]
    
    for test_case in invalid_configs:
        result = monitor_integration.update_monitor_config(test_case['config'])
        print(f"   {test_case['name']}: {'❌ 正确拒绝' if not result.get('success', True) else '✅ 错误接受'}")
        if not result.get('success', True):
            print(f"     错误信息: {result.get('message', 'N/A')}")
    
    # 4. 测试配置文件读写
    print("\n4. 测试配置文件读写:")
    
    # 读取原始配置文件
    auto_monitor_path = project_root / "config" / "auto_monitor.json"
    monitoring_path = project_root / "config" / "monitoring.json"
    
    print(f"   auto_monitor.json存在: {'✅' if auto_monitor_path.exists() else '❌'}")
    print(f"   monitoring.json存在: {'✅' if monitoring_path.exists() else '❌'}")
    
    # 检查备份文件是否创建
    auto_monitor_backup = auto_monitor_path.with_suffix('.bak')
    monitoring_backup = monitoring_path.with_suffix('.bak')
    
    print(f"   auto_monitor.json备份: {'✅' if auto_monitor_backup.exists() else '❌'}")
    print(f"   monitoring.json备份: {'✅' if monitoring_backup.exists() else '❌'}")
    
    # 5. 验证配置更新后的值
    print("\n5. 验证配置更新后的值:")
    updated_config = monitor_integration.get_monitor_config()
    
    if 'error' not in updated_config:
        print("   ✅ 更新后配置获取成功")
        print(f"   最大币种数: {updated_config.get('max_symbols', 'N/A')} (期望: 75)")
        print(f"   最小交易量: {updated_config.get('min_volume_usdt', 'N/A'):,} (期望: 2,000,000)")
        print(f"   置信度阈值: {updated_config.get('confidence_threshold', 'N/A')}% (期望: 70.0%)")
        print(f"   监控间隔: {updated_config.get('monitor_interval', 'N/A')} (期望: 2h)")
        print(f"   日志级别: {updated_config.get('log_level', 'N/A')} (期望: DEBUG)")
        print(f"   信号通知: {updated_config.get('enable_signal_notification', 'N/A')} (期望: False)")
        
        # 验证值是否正确更新
        checks = [
            ('max_symbols', 75),
            ('min_volume_usdt', 2000000),
            ('confidence_threshold', 70.0),
            ('monitor_interval', '2h'),
            ('log_level', 'DEBUG'),
            ('enable_signal_notification', False)
        ]
        
        all_correct = True
        for key, expected in checks:
            actual = updated_config.get(key)
            if actual == expected:
                print(f"     ✅ {key}: {actual}")
            else:
                print(f"     ❌ {key}: {actual} (期望: {expected})")
                all_correct = False
        
        if all_correct:
            print("   🎉 所有配置值更新正确！")
        else:
            print("   ⚠️ 部分配置值更新不正确")
    else:
        print(f"   ❌ 更新后配置获取失败: {updated_config['error']}")
    
    # 6. 测试恢复原始配置
    print("\n6. 测试恢复原始配置:")
    restore_config = {
        'max_symbols': 50,
        'min_volume_usdt': 1000000,
        'confidence_threshold': 60.0,
        'monitor_interval': '4h',
        'log_level': 'INFO',
        'enable_signal_notification': True
    }
    
    restore_result = monitor_integration.update_monitor_config(restore_config)
    print(f"   恢复结果: {'✅ 成功' if restore_result.get('success', False) else '❌ 失败'}")
    print(f"   消息: {restore_result.get('message', 'N/A')}")
    
    # 7. 测试配置文件内容
    print("\n7. 检查配置文件内容:")
    try:
        with open(auto_monitor_path, 'r', encoding='utf-8') as f:
            auto_config = json.load(f)
        
        print(f"   auto_monitor.json格式: {'✅ 有效JSON' if auto_config else '❌ 无效JSON'}")
        print(f"   monitor_settings存在: {'✅' if 'monitor_settings' in auto_config else '❌'}")
        print(f"   symbol_selection存在: {'✅' if 'symbol_selection' in auto_config else '❌'}")
        print(f"   strategy_settings存在: {'✅' if 'strategy_settings' in auto_config else '❌'}")
        print(f"   notification_settings存在: {'✅' if 'notification_settings' in auto_config else '❌'}")
        
    except Exception as e:
        print(f"   ❌ 读取auto_monitor.json失败: {e}")
    
    try:
        with open(monitoring_path, 'r', encoding='utf-8') as f:
            monitoring_config = json.load(f)
        
        print(f"   monitoring.json格式: {'✅ 有效JSON' if monitoring_config else '❌ 无效JSON'}")
        print(f"   interval存在: {'✅' if 'interval' in monitoring_config else '❌'}")
        
    except Exception as e:
        print(f"   ❌ 读取monitoring.json失败: {e}")
    
    print("\n" + "="*60)
    print("🎯 配置管理测试完成")
    print("="*60)

if __name__ == "__main__":
    test_config_management()
