#!/usr/bin/env python3
"""
监控系统集成模块
Monitor System Integration Module

提供Web界面与auto_4h_monitor系统的集成接口
"""

import asyncio
import threading
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
import logging
import sys
import psutil
import time
import os

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

try:
    from tools.auto_4h_monitor import Auto4HMonitor
    from src.data_layer.historical_data_fetcher import scan_local_databases
except ImportError as e:
    print(f"导入监控模块失败: {e}")

class MonitorIntegration:
    """监控系统集成类"""
    
    def __init__(self):
        """初始化监控集成"""
        self.monitor_instance = None
        self.monitor_thread = None
        self.is_running = False
        self.start_time = None
        self.data_dir = project_root / "data"
        self.alerts_db = self.data_dir / "alerts.db"
        self.status_file = project_root / "logs" / "monitor_status.json"

        # 确保目录存在
        self.data_dir.mkdir(exist_ok=True)
        (project_root / "logs").mkdir(exist_ok=True)

        # 初始化告警数据库
        self._init_alerts_db()

        # 恢复状态（如果存在）
        self._load_status()

    def _save_status(self):
        """保存监控状态到文件"""
        try:
            status_data = {
                'is_running': self.is_running,
                'start_time': self.start_time.isoformat() if self.start_time else None,
                'timestamp': datetime.now().isoformat(),
                'monitored_symbols': 50,  # 这里可以从监控实例获取实际数量
                'running': self.is_running,
                'status_message': 'running' if self.is_running else 'stopped'
            }

            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            print(f"保存状态文件失败: {e}")

    def _load_status(self):
        """从文件加载监控状态"""
        try:
            if self.status_file.exists():
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    status_data = json.load(f)

                # 检查状态文件的时效性
                timestamp = datetime.fromisoformat(status_data.get('timestamp', '1970-01-01'))
                age_minutes = (datetime.now() - timestamp).total_seconds() / 60

                # 如果状态文件超过10分钟，认为监控系统已经停止
                if age_minutes > 10:
                    self.is_running = False
                    self.start_time = None
                    self._save_status()  # 更新状态文件
                else:
                    self.is_running = status_data.get('is_running', False)
                    start_time_str = status_data.get('start_time')
                    if start_time_str:
                        self.start_time = datetime.fromisoformat(start_time_str)

        except Exception as e:
            print(f"加载状态文件失败: {e}")
            self.is_running = False
            self.start_time = None
    
    def _init_alerts_db(self):
        """初始化告警数据库"""
        try:
            conn = sqlite3.connect(str(self.alerts_db))
            conn.execute("""
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    symbol TEXT NOT NULL,
                    strategy TEXT NOT NULL,
                    signal_type TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    price REAL NOT NULL,
                    reason TEXT,
                    indicators TEXT,
                    notification_status TEXT,
                    execution_status TEXT
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON alerts(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_symbol ON alerts(symbol)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_strategy ON alerts(strategy)")
            
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"初始化告警数据库失败: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            status = {
                'monitor_running': self.is_running,
                'last_update': datetime.now().isoformat(),
                'monitored_symbols': 0,
                'total_alerts': 0,
                'recent_signals': 0,
                'next_kline_time': None,
                'current_kline_time': None
            }
            
            # 获取监控币种数量
            databases = scan_local_databases(str(self.data_dir))
            if databases:
                status['monitored_symbols'] = databases[0].get('unique_symbols', 0)
            
            # 获取告警统计
            alerts_stats = self.get_alerts_statistics()
            status.update(alerts_stats)
            
            # 获取K线时间信息
            if self.monitor_instance:
                try:
                    current_kline = self.monitor_instance._get_current_4h_kline_time()
                    next_kline = self.monitor_instance._get_next_4h_kline_time()
                    status['current_kline_time'] = current_kline.isoformat()
                    status['next_kline_time'] = next_kline.isoformat()
                except:
                    pass
            
            return status
        except Exception as e:
            return {'error': str(e)}
    
    def get_alerts_statistics(self) -> Dict[str, Any]:
        """获取告警统计信息"""
        try:
            if not self.alerts_db.exists():
                return {'total_alerts': 0, 'recent_signals': 0}
            
            conn = sqlite3.connect(str(self.alerts_db))
            
            # 总告警数
            cursor = conn.execute("SELECT COUNT(*) FROM alerts")
            total_alerts = cursor.fetchone()[0]
            
            # 最近24小时信号
            recent_time = (datetime.now() - timedelta(hours=24)).isoformat()
            cursor = conn.execute("SELECT COUNT(*) FROM alerts WHERE timestamp > ?", (recent_time,))
            recent_signals = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'total_alerts': total_alerts,
                'recent_signals': recent_signals
            }
        except Exception as e:
            return {'total_alerts': 0, 'recent_signals': 0, 'error': str(e)}
    
    def get_alerts_data(self, limit: int = 100, 
                       symbol_filter: List[str] = None,
                       strategy_filter: List[str] = None,
                       signal_filter: List[str] = None) -> pd.DataFrame:
        """获取告警数据"""
        try:
            if not self.alerts_db.exists():
                return pd.DataFrame()
            
            conn = sqlite3.connect(str(self.alerts_db))
            
            # 构建查询
            query = "SELECT * FROM alerts"
            params = []
            conditions = []
            
            if symbol_filter:
                placeholders = ','.join(['?' for _ in symbol_filter])
                conditions.append(f"symbol IN ({placeholders})")
                params.extend(symbol_filter)
            
            if strategy_filter:
                placeholders = ','.join(['?' for _ in strategy_filter])
                conditions.append(f"strategy IN ({placeholders})")
                params.extend(strategy_filter)
            
            if signal_filter:
                placeholders = ','.join(['?' for _ in signal_filter])
                conditions.append(f"signal_type IN ({placeholders})")
                params.extend(signal_filter)
            
            if conditions:
                query += " WHERE " + " AND ".join(conditions)
            
            query += " ORDER BY timestamp DESC"
            
            if limit:
                query += f" LIMIT {limit}"
            
            df = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            if not df.empty:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            return df
        except Exception as e:
            print(f"获取告警数据失败: {e}")
            return pd.DataFrame()
    
    def start_monitor(self, config: Dict[str, Any] = None) -> Dict[str, Any]:
        """启动监控系统"""
        try:
            if self.is_running:
                return {'success': False, 'message': '监控系统已在运行中'}

            # 创建监控实例，使用正确的路径
            config_path = str(project_root / "config")
            db_path = str(project_root / "data" / "usdt_historical_data.db")
            self.monitor_instance = Auto4HMonitor(config_path=config_path, db_path=db_path)
            
            # 在新线程中运行监控系统
            def run_monitor():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(self.monitor_instance.start())
                except Exception as e:
                    print(f"监控系统运行错误: {e}")
                    self.is_running = False
            
            self.monitor_thread = threading.Thread(target=run_monitor, daemon=True)
            self.monitor_thread.start()
            self.is_running = True
            self.start_time = datetime.now()

            # 保存状态
            self._save_status()

            return {
                'success': True,
                'message': '监控系统启动成功',
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'启动失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    def stop_monitor(self) -> Dict[str, Any]:
        """停止监控系统"""
        try:
            if not self.is_running:
                return {'success': False, 'message': '监控系统未运行'}
            
            self.is_running = False
            self.start_time = None

            if self.monitor_instance:
                # 异步停止监控系统
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(self.monitor_instance.stop())
                except:
                    pass

                self.monitor_instance = None

            # 保存状态
            self._save_status()

            return {
                'success': True,
                'message': '监控系统已停止',
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'停止失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    def get_monitor_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        try:
            # 读取auto_monitor.json配置
            auto_monitor_config = self._load_config_file('auto_monitor.json')

            # 读取monitoring.json配置
            monitoring_config = self._load_config_file('monitoring.json')

            # 合并配置
            config = {
                'monitor_interval': monitoring_config.get('interval', '4h'),
                'max_symbols': auto_monitor_config.get('monitor_settings', {}).get('max_symbols', 50),
                'min_volume_usdt': auto_monitor_config.get('monitor_settings', {}).get('min_volume_usdt', 1000000),
                'update_interval_hours': auto_monitor_config.get('monitor_settings', {}).get('update_interval_hours', 4),
                'data_retention_days': auto_monitor_config.get('monitor_settings', {}).get('data_retention_days', 90),

                'symbol_selection_mode': auto_monitor_config.get('symbol_selection', {}).get('mode', 'auto'),
                'priority_symbols': auto_monitor_config.get('symbol_selection', {}).get('priority_symbols', []),
                'exclude_symbols': auto_monitor_config.get('symbol_selection', {}).get('exclude_symbols', []),

                'min_data_points': auto_monitor_config.get('strategy_settings', {}).get('min_data_points', 50),
                'confidence_threshold': auto_monitor_config.get('strategy_settings', {}).get('confidence_threshold', 60.0),
                'max_signals_per_hour': auto_monitor_config.get('strategy_settings', {}).get('max_signals_per_hour', 10),

                'enable_startup_notification': auto_monitor_config.get('notification_settings', {}).get('enable_startup_notification', True),
                'enable_signal_notification': auto_monitor_config.get('notification_settings', {}).get('enable_signal_notification', True),
                'enable_error_notification': auto_monitor_config.get('notification_settings', {}).get('enable_error_notification', True),
                'log_level': auto_monitor_config.get('notification_settings', {}).get('log_level', 'INFO'),

                'health_check_interval_minutes': monitoring_config.get('health_check_interval_minutes', 30),
                'max_log_files': monitoring_config.get('max_log_files', 10),

                'strategies': ['EMABreakout'],
                'data_source': 'binance'
            }

            return config
        except Exception as e:
            return {'error': str(e)}
    
    def update_monitor_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """更新监控配置"""
        try:
            # 验证配置参数
            validation_result = self._validate_config(config)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': f'配置验证失败: {validation_result["error"]}',
                    'timestamp': datetime.now().isoformat()
                }

            # 更新auto_monitor.json
            auto_monitor_config = self._load_config_file('auto_monitor.json')

            # 更新monitor_settings
            if 'monitor_settings' not in auto_monitor_config:
                auto_monitor_config['monitor_settings'] = {}

            monitor_settings = auto_monitor_config['monitor_settings']
            if 'max_symbols' in config:
                monitor_settings['max_symbols'] = config['max_symbols']
            if 'min_volume_usdt' in config:
                monitor_settings['min_volume_usdt'] = config['min_volume_usdt']
            if 'update_interval_hours' in config:
                monitor_settings['update_interval_hours'] = config['update_interval_hours']
            if 'data_retention_days' in config:
                monitor_settings['data_retention_days'] = config['data_retention_days']

            # 更新symbol_selection
            if 'symbol_selection' not in auto_monitor_config:
                auto_monitor_config['symbol_selection'] = {}

            symbol_selection = auto_monitor_config['symbol_selection']
            if 'symbol_selection_mode' in config:
                symbol_selection['mode'] = config['symbol_selection_mode']
            if 'priority_symbols' in config:
                symbol_selection['priority_symbols'] = config['priority_symbols']
            if 'exclude_symbols' in config:
                symbol_selection['exclude_symbols'] = config['exclude_symbols']

            # 更新strategy_settings
            if 'strategy_settings' not in auto_monitor_config:
                auto_monitor_config['strategy_settings'] = {}

            strategy_settings = auto_monitor_config['strategy_settings']
            if 'min_data_points' in config:
                strategy_settings['min_data_points'] = config['min_data_points']
            if 'confidence_threshold' in config:
                strategy_settings['confidence_threshold'] = config['confidence_threshold']
            if 'max_signals_per_hour' in config:
                strategy_settings['max_signals_per_hour'] = config['max_signals_per_hour']

            # 更新notification_settings
            if 'notification_settings' not in auto_monitor_config:
                auto_monitor_config['notification_settings'] = {}

            notification_settings = auto_monitor_config['notification_settings']
            if 'enable_startup_notification' in config:
                notification_settings['enable_startup_notification'] = config['enable_startup_notification']
            if 'enable_signal_notification' in config:
                notification_settings['enable_signal_notification'] = config['enable_signal_notification']
            if 'enable_error_notification' in config:
                notification_settings['enable_error_notification'] = config['enable_error_notification']
            if 'log_level' in config:
                notification_settings['log_level'] = config['log_level']

            # 保存auto_monitor.json
            if not self._save_config_file('auto_monitor.json', auto_monitor_config):
                return {
                    'success': False,
                    'message': '保存auto_monitor.json失败',
                    'timestamp': datetime.now().isoformat()
                }

            # 更新monitoring.json
            monitoring_config = self._load_config_file('monitoring.json')

            if 'monitor_interval' in config:
                monitoring_config['interval'] = config['monitor_interval']
            if 'health_check_interval_minutes' in config:
                monitoring_config['health_check_interval_minutes'] = config['health_check_interval_minutes']
            if 'max_log_files' in config:
                monitoring_config['max_log_files'] = config['max_log_files']

            # 保存monitoring.json
            if not self._save_config_file('monitoring.json', monitoring_config):
                return {
                    'success': False,
                    'message': '保存monitoring.json失败',
                    'timestamp': datetime.now().isoformat()
                }

            # 如果监控系统正在运行，通知配置更改
            config_change_message = "配置更新成功"
            if self.is_running:
                config_change_message += "，重启监控系统后生效"

            return {
                'success': True,
                'message': config_change_message,
                'timestamp': datetime.now().isoformat(),
                'requires_restart': self.is_running
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'配置更新失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }

    def get_monitor_status(self) -> Dict[str, Any]:
        """获取监控系统状态"""
        try:
            # 计算运行时间
            uptime_seconds = 0
            if self.start_time and self.is_running:
                uptime_seconds = int((datetime.now() - self.start_time).total_seconds())

            status = {
                'is_running': self.is_running,
                'monitor_instance': self.monitor_instance is not None,
                'thread_alive': False,
                'start_time': self.start_time.isoformat() if self.start_time else None,
                'uptime_seconds': uptime_seconds,
                'uptime_formatted': self._format_uptime(uptime_seconds),
                'timestamp': datetime.now().isoformat(),
                'status_file_exists': self.status_file.exists()
            }

            # 检查线程状态
            if self.monitor_thread:
                status['thread_alive'] = self.monitor_thread.is_alive()
                # 如果线程死了但状态显示运行中，更新状态
                if not self.monitor_thread.is_alive() and self.is_running:
                    self.is_running = False
                    self.start_time = None
                    self._save_status()
                    status['is_running'] = False
                    status['status_message'] = 'thread_died'

            # 如果有监控实例，获取更多信息
            if self.monitor_instance:
                try:
                    status['monitor_type'] = 'Auto4HMonitor'
                    status['config_path'] = getattr(self.monitor_instance, 'config_path', 'unknown')
                    status['db_path'] = getattr(self.monitor_instance, 'db_path', 'unknown')
                except Exception as e:
                    status['monitor_error'] = str(e)

            return status

        except Exception as e:
            return {
                'error': f'获取监控状态失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }

    def _format_uptime(self, seconds: int) -> str:
        """格式化运行时间"""
        if seconds < 60:
            return f"{seconds}秒"
        elif seconds < 3600:
            minutes = seconds // 60
            secs = seconds % 60
            return f"{minutes}分{secs}秒"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{hours}小时{minutes}分钟"

    def _load_config_file(self, filename: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            config_path = project_root / "config" / filename
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                print(f"配置文件不存在: {config_path}")
                return {}
        except Exception as e:
            print(f"加载配置文件失败 {filename}: {e}")
            return {}

    def _save_config_file(self, filename: str, config: Dict[str, Any]) -> bool:
        """保存配置文件"""
        try:
            config_path = project_root / "config" / filename
            # 创建备份
            if config_path.exists():
                backup_path = config_path.with_suffix('.bak')
                import shutil
                shutil.copy2(config_path, backup_path)

            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            return True
        except Exception as e:
            print(f"保存配置文件失败 {filename}: {e}")
            return False

    def _validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证配置参数"""
        try:
            errors = []

            # 验证数值范围
            if 'max_symbols' in config:
                if not isinstance(config['max_symbols'], int) or config['max_symbols'] < 1 or config['max_symbols'] > 200:
                    errors.append("max_symbols必须是1-200之间的整数")

            if 'min_volume_usdt' in config:
                if not isinstance(config['min_volume_usdt'], (int, float)) or config['min_volume_usdt'] < 0:
                    errors.append("min_volume_usdt必须是非负数")

            if 'update_interval_hours' in config:
                if not isinstance(config['update_interval_hours'], (int, float)) or config['update_interval_hours'] < 1:
                    errors.append("update_interval_hours必须是大于0的数值")

            if 'data_retention_days' in config:
                if not isinstance(config['data_retention_days'], int) or config['data_retention_days'] < 1:
                    errors.append("data_retention_days必须是大于0的整数")

            if 'confidence_threshold' in config:
                if not isinstance(config['confidence_threshold'], (int, float)) or config['confidence_threshold'] < 0 or config['confidence_threshold'] > 100:
                    errors.append("confidence_threshold必须是0-100之间的数值")

            if 'max_signals_per_hour' in config:
                if not isinstance(config['max_signals_per_hour'], int) or config['max_signals_per_hour'] < 1:
                    errors.append("max_signals_per_hour必须是大于0的整数")

            if 'health_check_interval_minutes' in config:
                if not isinstance(config['health_check_interval_minutes'], int) or config['health_check_interval_minutes'] < 1:
                    errors.append("health_check_interval_minutes必须是大于0的整数")

            # 验证字符串选项
            if 'symbol_selection_mode' in config:
                if config['symbol_selection_mode'] not in ['auto', 'manual', 'priority']:
                    errors.append("symbol_selection_mode必须是auto、manual或priority之一")

            if 'monitor_interval' in config:
                # 监控系统目前只支持4小时间隔
                if config['monitor_interval'] not in ['4h']:
                    errors.append("monitor_interval目前只支持4h间隔")

            if 'log_level' in config:
                if config['log_level'] not in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
                    errors.append("log_level必须是有效的日志级别")

            # 验证列表类型
            if 'priority_symbols' in config:
                if not isinstance(config['priority_symbols'], list):
                    errors.append("priority_symbols必须是列表")
                else:
                    for symbol in config['priority_symbols']:
                        if not isinstance(symbol, str) or not symbol.endswith('USDT'):
                            errors.append(f"无效的交易对: {symbol}")

            if 'exclude_symbols' in config:
                if not isinstance(config['exclude_symbols'], list):
                    errors.append("exclude_symbols必须是列表")
                else:
                    for symbol in config['exclude_symbols']:
                        if not isinstance(symbol, str):
                            errors.append(f"无效的排除交易对: {symbol}")

            if errors:
                return {'valid': False, 'error': '; '.join(errors)}
            else:
                return {'valid': True}

        except Exception as e:
            return {'valid': False, 'error': f'验证过程出错: {str(e)}'}

    def apply_config_hot_update(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """热更新配置（无需重启监控系统）"""
        try:
            if not self.is_running or not self.monitor_instance:
                return {
                    'success': False,
                    'message': '监控系统未运行，无法进行热更新',
                    'timestamp': datetime.now().isoformat()
                }

            # 只有部分配置支持热更新
            hot_updateable_configs = [
                'log_level',
                'enable_signal_notification',
                'enable_error_notification',
                'health_check_interval_minutes',
                'max_signals_per_hour'
            ]

            applied_configs = []
            skipped_configs = []

            for key, value in config.items():
                if key in hot_updateable_configs:
                    try:
                        # 这里可以添加具体的热更新逻辑
                        # 例如更新监控实例的配置
                        applied_configs.append(f"{key}: {value}")
                    except Exception as e:
                        skipped_configs.append(f"{key}: {e}")
                else:
                    skipped_configs.append(f"{key}: 不支持热更新")

            message_parts = []
            if applied_configs:
                message_parts.append(f"已应用: {', '.join(applied_configs)}")
            if skipped_configs:
                message_parts.append(f"跳过: {', '.join(skipped_configs)}")

            return {
                'success': True,
                'message': '; '.join(message_parts) if message_parts else '无配置需要热更新',
                'applied_count': len(applied_configs),
                'skipped_count': len(skipped_configs),
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'热更新失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }

    def restart_monitor_with_new_config(self) -> Dict[str, Any]:
        """使用新配置重启监控系统"""
        try:
            if not self.is_running:
                return {
                    'success': False,
                    'message': '监控系统未运行，无需重启',
                    'timestamp': datetime.now().isoformat()
                }

            # 停止当前监控系统
            stop_result = self.stop_monitor()
            if not stop_result.get('success', False):
                return {
                    'success': False,
                    'message': f'停止监控系统失败: {stop_result.get("message", "unknown")}',
                    'timestamp': datetime.now().isoformat()
                }

            # 等待一秒确保完全停止
            import time
            time.sleep(1)

            # 重新启动监控系统
            start_result = self.start_monitor()
            if start_result.get('success', False):
                return {
                    'success': True,
                    'message': '监控系统已使用新配置重启',
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'success': False,
                    'message': f'重启监控系统失败: {start_result.get("message", "unknown")}',
                    'timestamp': datetime.now().isoformat()
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'重启过程出错: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }

    def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        try:
            health_data = {
                'timestamp': datetime.now().isoformat(),
                'overall_status': 'healthy',
                'issues': [],
                'warnings': []
            }

            # 1. 系统资源监控
            resource_status = self._get_resource_status()
            health_data['resources'] = resource_status

            # 检查资源警告
            if 'error' not in resource_status:
                cpu_percent = resource_status.get('cpu_percent', 0)
                memory_percent = resource_status.get('memory_percent', 0)
                disk_percent = resource_status.get('disk_percent', 0)

                if cpu_percent > 80:
                    health_data['warnings'].append(f"CPU使用率过高: {cpu_percent:.1f}%")

                if memory_percent > 85:
                    health_data['warnings'].append(f"内存使用率过高: {memory_percent:.1f}%")

                if disk_percent > 90:
                    health_data['warnings'].append(f"磁盘使用率过高: {disk_percent:.1f}%")
            else:
                health_data['issues'].append(f"资源监控失败: {resource_status['error']}")

            # 2. 监控系统状态检查
            monitor_health = self._check_monitor_health()
            health_data['monitor'] = monitor_health

            if monitor_health.get('issues'):
                health_data['issues'].extend(monitor_health['issues'])

            # 3. 数据库健康检查
            database_health = self._check_database_health()
            health_data['database'] = database_health

            if database_health.get('issues'):
                health_data['issues'].extend(database_health['issues'])

            # 4. 日志文件检查
            log_health = self._check_log_health()
            health_data['logs'] = log_health

            if log_health.get('warnings'):
                health_data['warnings'].extend(log_health['warnings'])

            # 5. 网络连接检查
            network_health = self._check_network_health()
            health_data['network'] = network_health

            if network_health.get('issues'):
                health_data['issues'].extend(network_health['issues'])

            # 6. 性能指标
            performance_metrics = self._get_performance_metrics()
            health_data['performance'] = performance_metrics

            # 确定整体状态
            if health_data['issues']:
                health_data['overall_status'] = 'critical'
            elif health_data['warnings']:
                health_data['overall_status'] = 'warning'
            else:
                health_data['overall_status'] = 'healthy'

            return health_data

        except Exception as e:
            return {
                'timestamp': datetime.now().isoformat(),
                'overall_status': 'error',
                'error': f'获取系统健康状态失败: {str(e)}',
                'issues': [f'健康检查系统故障: {str(e)}']
            }

    def _get_resource_status(self) -> Dict[str, Any]:
        """获取系统资源状态"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)

            # 内存使用情况
            memory = psutil.virtual_memory()

            # 磁盘使用情况
            disk = psutil.disk_usage('.')

            # 网络IO
            try:
                network_io = psutil.net_io_counters()
                network_bytes_sent = network_io.bytes_sent if network_io else 0
                network_bytes_recv = network_io.bytes_recv if network_io else 0
            except:
                network_bytes_sent = 0
                network_bytes_recv = 0

            return {
                'cpu_percent': float(cpu_percent),
                'cpu_count': int(psutil.cpu_count()),
                'memory_total_gb': round(float(memory.total) / (1024**3), 2),
                'memory_used_gb': round(float(memory.used) / (1024**3), 2),
                'memory_percent': float(memory.percent),
                'disk_total_gb': round(float(disk.total) / (1024**3), 2),
                'disk_used_gb': round(float(disk.used) / (1024**3), 2),
                'disk_percent': round((float(disk.used) / float(disk.total)) * 100, 1),
                'network_bytes_sent': int(network_bytes_sent),
                'network_bytes_recv': int(network_bytes_recv),
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {'error': f'获取资源状态失败: {str(e)}'}

    def _check_monitor_health(self) -> Dict[str, Any]:
        """检查监控系统健康状态"""
        try:
            issues = []
            warnings = []

            # 检查监控系统是否运行
            if not self.is_running:
                issues.append("监控系统未运行")
            else:
                # 检查线程状态
                if self.monitor_thread and not self.monitor_thread.is_alive():
                    issues.append("监控线程已死亡")

                # 检查运行时间
                if self.start_time:
                    uptime = (datetime.now() - self.start_time).total_seconds()
                    if uptime < 60:  # 运行时间少于1分钟可能表示频繁重启
                        warnings.append(f"监控系统运行时间较短: {uptime:.0f}秒")

            # 检查状态文件
            if not self.status_file.exists():
                warnings.append("监控状态文件不存在")
            else:
                try:
                    with open(self.status_file, 'r', encoding='utf-8') as f:
                        status_data = json.load(f)

                    # 检查状态文件时效性
                    timestamp = datetime.fromisoformat(status_data.get('timestamp', '1970-01-01'))
                    age_minutes = (datetime.now() - timestamp).total_seconds() / 60

                    if age_minutes > 10:
                        warnings.append(f"状态文件过期: {age_minutes:.1f}分钟前")

                except Exception as e:
                    warnings.append(f"状态文件读取失败: {str(e)}")

            return {
                'is_running': self.is_running,
                'thread_alive': self.monitor_thread.is_alive() if self.monitor_thread else False,
                'uptime_seconds': int((datetime.now() - self.start_time).total_seconds()) if self.start_time else 0,
                'issues': issues,
                'warnings': warnings,
                'status_file_exists': self.status_file.exists()
            }

        except Exception as e:
            return {
                'error': f'监控健康检查失败: {str(e)}',
                'issues': [f'监控健康检查故障: {str(e)}']
            }

    def _check_database_health(self) -> Dict[str, Any]:
        """检查数据库健康状态"""
        try:
            issues = []
            warnings = []

            # 检查数据库文件
            db_files = [
                self.data_dir / "usdt_historical_data.db",
                self.alerts_db
            ]

            db_status = {}
            for db_file in db_files:
                db_name = db_file.name
                if not db_file.exists():
                    issues.append(f"数据库文件不存在: {db_name}")
                    db_status[db_name] = {'exists': False}
                else:
                    try:
                        # 检查数据库连接
                        conn = sqlite3.connect(str(db_file))
                        cursor = conn.cursor()
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                        tables = cursor.fetchall()
                        conn.close()

                        # 检查文件大小
                        file_size_mb = db_file.stat().st_size / (1024 * 1024)

                        db_status[db_name] = {
                            'exists': True,
                            'tables_count': len(tables),
                            'size_mb': round(file_size_mb, 2),
                            'accessible': True
                        }

                        # 检查文件大小警告
                        if file_size_mb > 1000:  # 超过1GB
                            warnings.append(f"数据库文件过大: {db_name} ({file_size_mb:.1f}MB)")

                    except Exception as e:
                        issues.append(f"数据库连接失败: {db_name} - {str(e)}")
                        db_status[db_name] = {'exists': True, 'accessible': False, 'error': str(e)}

            return {
                'databases': db_status,
                'issues': issues,
                'warnings': warnings
            }

        except Exception as e:
            return {
                'error': f'数据库健康检查失败: {str(e)}',
                'issues': [f'数据库健康检查故障: {str(e)}']
            }

    def get_data_summary(self) -> Dict[str, Any]:
        """获取数据概览"""
        try:
            databases = scan_local_databases(str(self.data_dir))
            
            if not databases:
                return {'error': '未找到数据库'}
            
            summary = {
                'databases': len(databases),
                'total_symbols': 0,
                'total_klines': 0,
                'date_range': {},
                'intervals': []
            }
            
            for db_info in databases:
                summary['total_symbols'] += db_info.get('unique_symbols', 0)
                summary['total_klines'] += db_info.get('total_klines', 0)
                
                if 'earliest_date' in db_info and 'latest_date' in db_info:
                    summary['date_range'] = {
                        'start': db_info['earliest_date'],
                        'end': db_info['latest_date']
                    }
                
                if 'interval' in db_info:
                    summary['intervals'].append(db_info['interval'])
            
            summary['intervals'] = list(set(summary['intervals']))
            
            return summary
        except Exception as e:
            return {'error': str(e)}

    def _check_log_health(self) -> Dict[str, Any]:
        """检查日志文件健康状态"""
        try:
            warnings = []

            log_dir = project_root / "logs"
            log_files = {
                'trading.log': log_dir / "trading.log",
                'monitor_status.json': self.status_file
            }

            log_status = {}
            for log_name, log_path in log_files.items():
                if not log_path.exists():
                    warnings.append(f"日志文件不存在: {log_name}")
                    log_status[log_name] = {'exists': False}
                else:
                    try:
                        file_size_mb = log_path.stat().st_size / (1024 * 1024)

                        # 检查最后修改时间
                        last_modified = datetime.fromtimestamp(log_path.stat().st_mtime)
                        age_hours = (datetime.now() - last_modified).total_seconds() / 3600

                        log_status[log_name] = {
                            'exists': True,
                            'size_mb': round(file_size_mb, 2),
                            'last_modified': last_modified.isoformat(),
                            'age_hours': round(age_hours, 1)
                        }

                        # 检查日志文件大小
                        if file_size_mb > 100:  # 超过100MB
                            warnings.append(f"日志文件过大: {log_name} ({file_size_mb:.1f}MB)")

                        # 检查日志文件是否太久没更新
                        if age_hours > 24:  # 超过24小时没更新
                            warnings.append(f"日志文件长时间未更新: {log_name} ({age_hours:.1f}小时)")

                    except Exception as e:
                        warnings.append(f"检查日志文件失败: {log_name} - {str(e)}")
                        log_status[log_name] = {'exists': True, 'error': str(e)}

            return {
                'log_files': log_status,
                'warnings': warnings
            }

        except Exception as e:
            return {
                'error': f'日志健康检查失败: {str(e)}',
                'warnings': [f'日志健康检查故障: {str(e)}']
            }

    def _check_network_health(self) -> Dict[str, Any]:
        """检查网络连接健康状态"""
        try:
            issues = []

            # 检查网络连接
            network_status = {
                'internet_accessible': False,
                'binance_api_accessible': False,
                'response_times': {}
            }

            # 简单的网络连接检查
            try:
                import socket
                socket.create_connection(("8.8.8.8", 53), timeout=3)
                network_status['internet_accessible'] = True
            except:
                issues.append("无法访问互联网")

            # 检查Binance API连接（简单测试）
            try:
                import urllib.request
                start_time = time.time()
                urllib.request.urlopen("https://api.binance.com/api/v3/ping", timeout=5)
                response_time = (time.time() - start_time) * 1000
                network_status['binance_api_accessible'] = True
                network_status['response_times']['binance_api'] = round(response_time, 2)
            except:
                issues.append("无法访问Binance API")

            return {
                'status': network_status,
                'issues': issues
            }

        except Exception as e:
            return {
                'error': f'网络健康检查失败: {str(e)}',
                'issues': [f'网络健康检查故障: {str(e)}']
            }

    def _get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        try:
            metrics = {
                'timestamp': datetime.now().isoformat()
            }

            # 监控系统性能指标
            if self.is_running and self.start_time:
                uptime_seconds = (datetime.now() - self.start_time).total_seconds()
                metrics['monitor_uptime_seconds'] = int(uptime_seconds)
                metrics['monitor_uptime_formatted'] = self._format_uptime(int(uptime_seconds))
            else:
                metrics['monitor_uptime_seconds'] = 0
                metrics['monitor_uptime_formatted'] = "未运行"

            # 数据库查询性能测试
            try:
                start_time = time.time()
                conn = sqlite3.connect(str(self.alerts_db))
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM alerts")
                result = cursor.fetchone()
                conn.close()
                query_time = (time.time() - start_time) * 1000

                metrics['db_query_time_ms'] = round(query_time, 2)
                metrics['total_alerts'] = result[0] if result else 0
            except Exception as e:
                metrics['db_query_error'] = str(e)

            # 配置文件读取性能
            try:
                start_time = time.time()
                self._load_config_file('auto_monitor.json')
                config_load_time = (time.time() - start_time) * 1000
                metrics['config_load_time_ms'] = round(config_load_time, 2)
            except Exception as e:
                metrics['config_load_error'] = str(e)

            return metrics

        except Exception as e:
            return {
                'error': f'获取性能指标失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }

# 创建全局集成实例
monitor_integration = MonitorIntegration()
